<div class="container-fluid p-4" x-data="{ showFilterPopup: false }">
    <!-- Header -->
    <header class="d-flex justify-content-between align-items-center mb-4 p-3 bg-white rounded-3 shadow-sm">
        <div>
            <h1 class="main-header mb-1">Analytics</h1>
        </div>
        <div class="header-actions d-flex align-items-center gap-2">

            <!-- Filter Button - opens Alpine.js popup -->
            <button class="btn shadow-sm"
                style="height: 44px; border-color: #e2e2e2; margin:0 18px; cursor: pointer; background: white;"
                @click="showFilterPopup = true" data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-title="Filter">
                <img src="{{ asset('img/Vector10.png') }}" width="24" alt="Filter">
            </button>

            <!-- Refresh Button - Livewire action only -->
            <button class="btn shadow-sm"
                style="height: 44px; border-color: #e2e2e2; cursor: pointer; background: white;"
                wire:click="resetFilters" data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-title="Refresh">
                <img src="{{ asset('img/loading.png') }}" width="24" alt="Refresh">
            </button>

            <!-- Test SLA Charts Button -->
            <button class="btn btn-warning shadow-sm"
                style="height: 44px; cursor: pointer; font-size: 12px; padding: 0 10px;" wire:click="testChartUpdates"
                data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-title="Test SLA Charts">
                Test Charts
            </button>



            <!-- Analytics Date Filter Dropdown - Improved -->
            <div class="analytics-date-dropdown">
                <button class="analytics-date-btn {{ $fromDate && $toDate ? 'has-custom-range' : '' }}"
                    onclick="AnalyticsDateFilter.toggle()" type="button">
                    @if ($fromDate && $toDate)
                        {{ \Carbon\Carbon::parse($fromDate)->format('M d') }} -
                        {{ \Carbon\Carbon::parse($toDate)->format('M d, Y') }}
                    @else
                        {{ $selectedDateFilter ?: 'Choose Date' }}
                    @endif
                    <svg class="analytics-dropdown-arrow" width="12" height="8" viewBox="0 0 12 8"
                        fill="none">
                        <path d="M1 1L6 6L11 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                </button>
                <div class="analytics-date-menu" id="analyticsDateOptions">
                    <div class="analytics-date-item" wire:click="$set('selectedDateFilter', '24 Hours')"
                        onclick="AnalyticsDateFilter.close()">
                        Last 24 Hours
                    </div>
                    <div class="analytics-date-divider"></div>
                    <div class="analytics-date-item" wire:click="$set('selectedDateFilter', '7 Days')"
                        onclick="AnalyticsDateFilter.close()">
                        Last 7 Days
                    </div>
                    <div class="analytics-date-divider"></div>
                    <div class="analytics-date-item" wire:click="$set('selectedDateFilter', '30 Days')"
                        onclick="AnalyticsDateFilter.close()">
                        Last 30 Days
                    </div>
                    <div class="analytics-date-divider"></div>
                    <div class="analytics-date-item" wire:click="$set('selectedDateFilter', '60 Days')"
                        onclick="AnalyticsDateFilter.close()">
                        Last 60 Days
                    </div>
                    <div class="analytics-date-divider"></div>
                    <div class="analytics-date-item"
                        onclick="AnalyticsDateFilter.close(); @this.call('toggleFilterPopup')">
                        Custom Range...
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Analytics Filter Modal - New Classes -->
    <div x-show="showFilterPopup" @click.away="showFilterPopup = false" class="analytics-filter-overlay"
        style="display: none;">
        <div class="analytics-filter-modal" @click.stop style="width:520px; max-width:98vw; font-size:13px;">
            <div class="analytics-filter-header sticky-header"
                style="position:sticky; top:0; z-index:10; background: linear-gradient(90deg, #05b130 0%, #039927 57%, #c3df00 100%); color: #fff; border-radius: 15px 15px 0 0; padding: 10px 18px; display: flex; align-items: center; justify-content: space-between; min-height: 38px;">
                <h3 class="analytics-filter-title"
                    style="margin:0; font-size: 1rem; font-weight: 600; color: #fff; letter-spacing:0.5px;">Filter</h3>
                <button type="button" class="analytics-filter-close" @click="showFilterPopup = false"
                    style="background:none; border:none; color:#fff;">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                </button>
            </div>

            <div class="analytics-filter-section">
                <h4 class="analytics-filter-section-title">Type</h4>
                <div class="analytics-filter-options">
                    {{-- <label class="analytics-filter-option">
                        <input type="radio" wire:model.live="selectedChannel" value=""
                            class="analytics-filter-radio">
                        <span class="analytics-filter-label">
                            <i class="fas fa-globe analytics-filter-icon" style="color: #6c757d;"></i>
                            All
                        </span>
                    </label> --}}
                    <label class="analytics-filter-option">
                        <input type="radio" wire:model.live="selectedChannel" value="Voice"
                            class="analytics-filter-radio">
                        <span class="analytics-filter-label">
                            <img src="{{ asset('img/voice_ay.png') }}" alt="Voice" width="15" height="15"
                                class="analytics-filter-icon">
                            Voice
                        </span>
                    </label>
                    <label class="analytics-filter-option">
                        <input type="radio" wire:model.live="selectedChannel" value="Non-Voice"
                            class="analytics-filter-radio">
                        <span class="analytics-filter-label">
                            <i class="fas fa-comment analytics-filter-icon" style="color: #28a745;"></i>
                            Non-Voice
                        </span>
                    </label>
                </div>
            </div>

            @if ($selectedChannel === 'Non-Voice')
                <div class="analytics-filter-section">
                    <h4 class="analytics-filter-section-title">Social Media Channels</h4>
                    <div class="analytics-filter-grid">

                        <label class="analytics-filter-option">
                            <input type="checkbox" wire:model.defer="selectedSocialChannels" value="Facebook"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <img src="{{ asset('img/<EMAIL>') }}" alt="Facebook" width="15"
                                    height="15" class="analytics-filter-icon">
                                Facebook
                            </span>
                        </label>
                        <label class="analytics-filter-option">
                            <input type="checkbox" wire:model.defer="selectedSocialChannels" value="Whatsapp"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <img src="{{ asset('img/whatsapp.png') }}" alt="WhatsApp" width="15"
                                    height="15" class="analytics-filter-icon">
                                Whatsapp
                            </span>
                        </label>
                        <label class="analytics-filter-option">
                            <input type="checkbox" wire:model.defer="selectedSocialChannels" value="Web Chat"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <img src="{{ asset('img/web_chat_color.png') }}" alt="Web Chat" width="15"
                                    height="15" class="analytics-filter-icon">
                                Web Chat
                            </span>
                        </label>
                        <label class="analytics-filter-option">
                            <input type="checkbox" wire:model.defer="selectedSocialChannels" value="Email"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <img src="{{ asset('img/mail.png') }}" alt="Email" width="15" height="15"
                                    class="analytics-filter-icon">
                                Email
                            </span>
                        </label>
                        <label class="analytics-filter-option">
                            <input type="checkbox" wire:model.defer="selectedSocialChannels" value="Instagram"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <img src="{{ asset('img/instagram_color.png') }}" alt="Instagram" width="15"
                                    height="15" class="analytics-filter-icon">
                                Instagram
                            </span>
                        </label>
                        <label class="analytics-filter-option">
                            <input type="checkbox" wire:model.defer="selectedSocialChannels" value="Twitter"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <img src="{{ asset('img/twitter_color.png') }}" alt="Twitter" width="15"
                                    height="15" class="analytics-filter-icon">
                                Twitter
                            </span>
                        </label>
                        <label class="analytics-filter-option">
                            <input type="checkbox" wire:model.defer="selectedSocialChannels" value="Google"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <i class="fa-brands fa-google analytics-filter-icon" style="color: #02a34e;"></i>
                                Google
                            </span>
                        </label>

                    </div>
                </div>
            @endif
            <!-- Analytics Date Range Filter -->
            <div class="analytics-filter-section">
                <h4 class="analytics-filter-section-title">
                    Custom Date Range
                    @if ($fromDate && $toDate)
                        <small class="text-success" style="font-size: 12px; font-weight: normal;">
                            (Active)
                        </small>
                    @endif
                </h4>
                <div class="analytics-date-range">
                    <div class="analytics-date-input">
                        <label class="analytics-date-label">From Date</label>
                        <input type="date" class="analytics-date-field" wire:model.defer="fromDate"
                            max="{{ date('Y-m-d') }}">
                    </div>
                    <div class="analytics-date-input">
                        <label class="analytics-date-label">To Date</label>
                        <input type="date" class="analytics-date-field" wire:model.defer="toDate"
                            max="{{ date('Y-m-d') }}" min="{{ $fromDate }}">
                    </div>
                </div>
                @if ($fromDate && $toDate)
                    <div class="mt-2">
                        <small class="text-muted">
                            Selected range: {{ \Carbon\Carbon::parse($fromDate)->format('M d, Y') }} -
                            {{ \Carbon\Carbon::parse($toDate)->format('M d, Y') }}
                            ({{ \Carbon\Carbon::parse($fromDate)->diffInDays(\Carbon\Carbon::parse($toDate)) + 1 }}
                            days)
                        </small>
                    </div>
                @endif
            </div>

            <!-- Analytics Filter Actions -->
            <div class="analytics-filter-actions">
                <div style="width:100%; display:flex; justify-content:center; align-items:center;">
                    <button type="button" class="analytics-filter-apply" wire:click="applyFilters"
                        @click="showFilterPopup = false"
                        style="min-width: 140px; font-size: 13px; padding: 7px 0; display: flex; align-items: center; justify-content: center;">
                        Show Results
                    </button>
                </div>


            </div>
        </div>
    </div>

    <!-- First Row -->
    <div class="row g-3 mb-3">
        <!-- Received Trend Chart - Suitable Width -->
        <div class="col-lg-6 d-flex align-items-stretch">
            <div class="card shadow rounded border w-100" style="border-radius: 1em; border: 1px solid #d6d6d6">
                <div class="card-body d-flex flex-column" style="padding: 2.5vh 1.8vw">
                    <div class="chart-header" style="margin-bottom: 20px">
                        <h4 style="margin: 0; font-size: 1.25rem; font-weight: 600">
                            Received Trend
                        </h4>

                        <hr class="head-seperator" style="margin: 20px 0 0 0; border-color: #d6d6d6" />
                    </div>
                    <div class="flex-grow-1"
                        style="position: relative; height: 280px; min-height: 280px; max-height: 280px;">
                        <div style="width: 100%; height: 280px;">
                            <canvas id="RecivedticketsChart"
                                style="height: 280px !important; width: 100% !important;"></canvas>
                        </div>
                    </div>
                    <div class="chart-legend"
                        style="
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  margin-top: 12px;
                ">
                        <div
                            style="
                    width: 9px;
                    height: 9px;
                    background-color: #01a44f;
                    border-radius: 50%;
                  ">
                        </div>
                        <span style="font-size: 14px; color: #8f9090">Received</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Two Card Column -->
        <div class="col-lg-3 d-flex flex-column m-0" style="justify-content: space-between">
            <!-- SLA Performance Circles Card -->
            <div class="card p-2 mb-0 flex-grow-1" style="min-height: 0;margin-top: 2vh;">
                <div class="card-body d-flex align-items-center justify-content-center h-100" style="padding: 8px">
                    <div class="row w-100 align-items-center justify-content-center text-center">
                        <div class="col-4" style="position: relative">
                            <div
                                style="
                      height: 90px;
                      width: 90px;
                      position: relative;
                    ">
                                <canvas id="resolutionSlaChart"></canvas>
                                <div
                                    style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 14px; font-weight: bold; color: #000;">
                                    {{ $resolutionSlaPerformance }}%
                                </div>
                            </div>
                            <h6 class="mt-1 small" style="font-size: 11px; font-weight: 400; line-height: 1.1">
                                Resolution SLA<br />Performance %
                            </h6>
                            <div
                                style="
                      position: absolute;
                      right: 0;
                      top: 15%;
                      height: 70%;
                      width: 1px;
                      background: #d6d6d6;
                    ">
                            </div>
                        </div>
                        <div class="col-4" style="position: relative">
                            <div
                                style="
                      height: 90px;
                      width: 90px;
                      position: relative;
                    ">
                                <canvas id="responseSlaChart"></canvas>
                                <div
                                    style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 14px; font-weight: bold; color: #000;">
                                    {{ $responseSlaPerformance }}%
                                </div>
                            </div>
                            <h6 class="mt-1 small" style="font-size: 11px; font-weight: 400; line-height: 1.1">
                                Response SLA<br />Performance %
                            </h6>

                        </div>
                        <!-- Removed SLA Resolution % Chart -->
                    </div>
                </div>
            </div>
            <!-- Gauge Charts Card -->
            <div class="card p-2 flex-grow-1 mt-2" style="min-height: 0">
                <div class="card-body d-flex align-items-center justify-content-center h-100" style="padding: 8px">
                    <div class="row w-100 align-items-center text-center" style="position: relative">
                        <div class="col-6" style="position: relative">
                            <div class="gauge-chart-container"
                                style="
                      width: 130px;
                      height: 65px;
                      margin: 0 auto;
                      position: relative;
                    ">
                                <canvas id="avgResponseTimeChart"></canvas>
                                <div
                                    style="
                        position: absolute;
                        left: 50%;
                        top: 80%;
                        transform: translate(-50%, -50%);
                        width: 100%;
                        text-align: center;
                        pointer-events: none;
                      ">
                                    <span
                                        style="
                          font-size: 12px;
                          font-weight: bold;
                          color: #000000;
                          border-radius: 6px;
                          padding: 1px 4px;
                        ">{{ $averageResponseTime }}</span>
                                </div>
                            </div>
                            <h6 class="mt-1 small" style="font-size: 10px; line-height: 1.1">
                                Average<br />Response Time
                            </h6>

                        </div>
                        <div class="col-6" style="position: relative">
                            <div class="gauge-chart-container" wire:ignore
                                style="
                      width: 130px;
                      height: 65px;
                      margin: 0 auto;
                      position: relative;
                    ">
                                <canvas id="ticketResolutionTimeChart"></canvas>
                                <div id="resolutionTimeDisplay"
                                    style="
                        position: absolute;
                        left: 50%;
                        top: 80%;
                        transform: translate(-50%, -50%);
                        width: 100%;
                        text-align: center;
                        pointer-events: none;
                      ">
                                    <span
                                        style="
                          font-size: 12px;
                          font-weight: bold;
                          color: #000000;
                          border-radius: 6px;
                          padding: 1px 4px;
                        ">{{ $averageResolutionTime }}</span>
                                </div>
                            </div>
                            <h6 class="mt-1 small" style="font-size: 10px; line-height: 1.1">
                                Aggregate<br />Ticket Resolution Time
                            </h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Numbers Chart - Made Narrower -->
        <div class="col-lg-3 d-flex align-items-stretch">
            <div class="card p-2 h-100" style="padding: 0.4rem !important; width: 100%">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h5 class="chart-card-header" style="font-size: 0.9rem">
                            Total Numbers Of Received Tickets
                        </h5>
                        <span class="badge bg-light text-success"
                            style="font-size: 1rem; font-weight: 500; padding: 4px 5px">{{ number_format($totalReceivedTickets) }}</span>
                    </div>
                    <div class="ticket-status-container flex-grow-1" style="overflow-y: auto;">
                        @if (is_array($statusStats) && count($statusStats) > 0)
                            @foreach ($statusStats as $status)
                                <div class="ticket-status-item" style="margin-bottom: 3.5%;">
                                    <div class="progress-label">
                                        <span style="font-size: 13px">{{ $status['name'] }}
                                            ({{ number_format($status['count'] ?? 0) }})
                                        </span>
                                        <strong>{{ $status['percentage'] ?? 0 }}%</strong>
                                    </div>
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar {{ $status['name'] == 'Currently Active' ? 'bg-warning' : 'bg-success' }}"
                                            role="progressbar" style="width: {{ $status['percentage'] ?? 0 }}%"
                                            aria-valuenow="{{ $status['percentage'] ?? 0 }}" aria-valuemin="0"
                                            aria-valuemax="100"></div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <p>No ticket status data available.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row -->
    <div class="row g-3">
        <!-- SLA Violation Charts -->
        <div class="col-lg-6">
            <div class="card p-0 h-100">
                <div class="card-body p-0 d-flex flex-column justify-content-around">
                    <div class="sla-item">
                        <p>Number Of SLA - Response Violated Tickets</p>
                        <div class="chart-container" style="width: 168px; height: 54px">
                            <canvas id="slaResponseChart"></canvas>
                        </div>
                        <span class="stat-value">{{ $responseViolatedTickets }}</span>
                    </div>
                    <hr class="my-0" style="border-color: #d6d6d6" />
                    <div class="sla-item">
                        <p>Number Of SLA - Resolution Violated Tickets</p>
                        <div class="chart-container" style="width: 168px; height: 61px">
                            <canvas id="slaResolutionChart"></canvas>
                        </div>
                        <span class="stat-value text-danger">{{ $resolutionViolatedTickets }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top 5 Ticket Types -->
        <div class="col-lg-6">
            <div class="ticket-card">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="mb-0" style="font-size: 1.2rem">Top 5 Ticket Types</h4>
                    <button class="expand-btn" style="font-size: 0.9rem; padding: 0.5rem 1rem"
                        onclick="expandView()">
                        Expand
                        <i class="fas fa-chevron-right" style="font-size: 0.8rem"></i>
                    </button>
                </div>

                <div class="flow-container" id="flowContainer" style="padding: 1.5vh 2vw">
                    <div class="main-node flow-node" style="font-size: 1rem">
                        Top 10 Tickets
                    </div>
                    <svg class="connection-svg" id="connectionSvg" style="width: 100%; height: auto"></svg>
                    <div class="ticket-list" id="ticketList" style="font-size: 0.9rem"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter CSS - Exact same as old analytics -->
